'use client';

import React, { useState, useEffect } from 'react';
import { Button, Input, Card, CardHeader, CardTitle, CardContent, useConfirmDialog, useMessageBox } from '@/components/ui';
import { MiningRig, Cryptocurrency } from '@/components/icons';
import { Calculator, DollarSign, Zap } from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';

interface PurchaseMiningUnitProps {
  onPurchaseComplete?: () => void;
}

export const PurchaseMiningUnit: React.FC<PurchaseMiningUnitProps> = ({
  onPurchaseComplete,
}) => {
  const [formData, setFormData] = useState({
    thsAmount: '',
    investmentAmount: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [thsPrice, setThsPrice] = useState(50); // Default price
  const [earningsRanges, setEarningsRanges] = useState([
    { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
    { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
    { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
  ]);
  const [minPurchaseAmount, setMinPurchaseAmount] = useState(100);

  // Dialog hooks
  const { showConfirm, hideConfirm, ConfirmDialog } = useConfirmDialog();
  const { showMessage, hideMessage, MessageBoxComponent } = useMessageBox();

  // Fetch current TH/s price and ROI range
  useEffect(() => {
    fetchPricing();
  }, []);

  const fetchPricing = async () => {
    try {
      const response = await fetch('/api/admin/settings/pricing');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setThsPrice(data.data.thsPrice);
          setEarningsRanges(data.data.earningsRanges);
          setMinPurchaseAmount(data.data.minPurchaseAmount);
        }
      }
    } catch (error) {
      console.error('Failed to fetch pricing:', error);
    }
  };

  const handleThsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const ths = parseFloat(e.target.value) || 0;
    const investment = ths * thsPrice;
    
    setFormData({
      thsAmount: e.target.value,
      investmentAmount: investment > 0 ? investment.toFixed(2) : '',
    });
  };

  const handleInvestmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const investment = parseFloat(e.target.value) || 0;
    const ths = investment / thsPrice;

    setFormData({
      thsAmount: ths > 0 ? ths.toFixed(4) : '',
      investmentAmount: e.target.value,
    });
  };

  // Get the appropriate earnings range for a given TH/s amount
  const getEarningsRangeForTHS = (thsAmount: number) => {
    const range = earningsRanges.find(range =>
      thsAmount >= range.minTHS && thsAmount <= range.maxTHS
    );
    return range || earningsRanges[earningsRanges.length - 1]; // Fallback to highest range
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      const thsAmount = parseFloat(formData.thsAmount);
      const investmentAmount = parseFloat(formData.investmentAmount);

      if (!thsAmount || !investmentAmount || thsAmount <= 0 || investmentAmount <= 0) {
        throw new Error('Please enter valid amounts');
      }

      if (investmentAmount < minPurchaseAmount) {
        throw new Error(`Minimum purchase amount is $${minPurchaseAmount}`);
      }

      // Calculate estimated earnings for confirmation
      const { daily, weekly, monthly, range } = calculateEstimatedEarnings();

      // Show confirmation dialog
      showConfirm({
        title: 'Confirm Mining Unit Purchase',
        message: (
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">TH/s Amount:</span>
                <span className="font-semibold">{thsAmount.toFixed(4)} TH/s</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Investment Amount:</span>
                <span className="font-semibold">${investmentAmount.toFixed(2)} USD</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Price per TH/s:</span>
                <span className="font-semibold">${thsPrice.toFixed(2)} USD</span>
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg space-y-2">
              <h4 className="font-semibold text-green-800">Estimated Earnings (Average ROI: {range ? `${range.dailyReturnMin}-${range.dailyReturnMax}%` : '0.4%'})</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-green-600">${daily.toFixed(2)}</div>
                  <div className="text-green-700">Daily</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-green-600">${weekly.toFixed(2)}</div>
                  <div className="text-green-700">Weekly</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-green-600">${monthly.toFixed(2)}</div>
                  <div className="text-green-700">Monthly</div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-3 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Important:</strong> Mining units are active for 24 months and expire when 5x investment is earned.
              </p>
            </div>
          </div>
        ),
        confirmText: 'Purchase Mining Unit',
        cancelText: 'Cancel',
        variant: 'default',
        onConfirm: () => processPurchase(thsAmount, investmentAmount),
      });

    } catch (err: any) {
      setError(err.message || 'Invalid purchase details');
    }
  };

  const processPurchase = async (thsAmount: number, investmentAmount: number) => {
    setLoading(true);

    try {
      const response = await fetch('/api/mining-units', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          thsAmount,
          investmentAmount,
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Purchase failed');
      }

      // Show success message
      showMessage({
        title: 'Mining Unit Purchased',
        message: 'Your mining unit has been purchased successfully! You will start earning daily returns within 24 hours.',
        variant: 'success',
        buttonText: 'OK',
      });

      // Reset form
      setFormData({
        thsAmount: '',
        investmentAmount: '',
      });

      // Notify parent component
      if (onPurchaseComplete) {
        onPurchaseComplete();
      }

    } catch (err: any) {
      showMessage({
        title: 'Purchase Failed',
        message: err.message || 'Failed to purchase mining unit. Please try again.',
        variant: 'error',
        buttonText: 'OK',
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateEstimatedEarnings = () => {
    const investment = parseFloat(formData.investmentAmount) || 0;
    const thsAmount = parseFloat(formData.thsAmount) || 0;
    if (investment <= 0 || thsAmount <= 0) return { daily: 0, weekly: 0, monthly: 0, range: null };

    const range = getEarningsRangeForTHS(thsAmount);
    const avgROI = (range.dailyReturnMin + range.dailyReturnMax) / 2;
    const daily = (investment * avgROI) / 100;

    return {
      daily,
      weekly: daily * 7,
      monthly: daily * 30,
      range,
    };
  };

  const estimatedEarnings = calculateEstimatedEarnings();

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center space-x-3 text-xl">
          <div className="h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center">
            <MiningRig className="h-6 w-6 text-solar-600" />
          </div>
          <span>Purchase Mining Power</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <form onSubmit={handleSubmit} className="space-y-8">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-xl text-sm font-medium">
              {error}
            </div>
          )}

          {/* Current Pricing Info */}
          <div className="bg-gradient-to-r from-solar-50 to-eco-50 rounded-xl p-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
              <div className="text-center sm:text-left">
                <span className="text-sm font-medium text-gray-600 block mb-1">Current TH/s Price</span>
                <span className="text-2xl font-bold text-solar-600">
                  {formatCurrency(thsPrice)} / TH/s
                </span>
              </div>
              <div className="text-center sm:text-right">
                <span className="text-sm font-medium text-gray-600 block mb-1">
                  {estimatedEarnings.range ?
                    `Daily ROI Range (${estimatedEarnings.range.minTHS}-${estimatedEarnings.range.maxTHS} TH/s)` :
                    'Daily ROI Range'
                  }
                </span>
                <span className="text-xl font-bold text-eco-600">
                  {estimatedEarnings.range ?
                    `${estimatedEarnings.range.dailyReturnMin}% - ${estimatedEarnings.range.dailyReturnMax}%` :
                    '0.3% - 0.7%'
                  }
                </span>
              </div>
            </div>

            {/* Dynamic Earnings Ranges */}
            <div className="border-t border-gray-200 pt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-3">TH/s Based Earnings Tiers</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {earningsRanges.map((range, index) => (
                  <div key={index} className="bg-white rounded-lg p-3 border border-gray-200">
                    <div className="text-xs font-medium text-gray-600 mb-1">
                      {range.minTHS} - {range.maxTHS === 999999 ? '∞' : range.maxTHS} TH/s
                    </div>
                    <div className="text-sm font-bold text-eco-600">
                      {range.dailyReturnMin}% - {range.dailyReturnMax}%
                    </div>
                    <div className="text-xs text-gray-500">
                      Monthly: {range.monthlyReturnMin}% - {range.monthlyReturnMax}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Input Fields */}
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <Input
                  label="TH/s Amount"
                  type="number"
                  step="0.0001"
                  min="0"
                  value={formData.thsAmount}
                  onChange={handleThsChange}
                  placeholder="Enter TH/s amount"
                  leftIcon={<Zap className="h-4 w-4" />}
                  className="h-12"
                />
              </div>

              <div>
                <Input
                  label="Investment Amount (USD)"
                  type="number"
                  step="0.01"
                  min={minPurchaseAmount}
                  value={formData.investmentAmount}
                  onChange={handleInvestmentChange}
                  placeholder="Enter investment amount"
                  leftIcon={<DollarSign className="h-4 w-4" />}
                  className="h-12"
                />
              </div>
            </div>
          </div>

          {/* Estimated Earnings */}
          {estimatedEarnings.daily > 0 && (
            <div className="bg-gradient-to-r from-eco-50 to-green-50 rounded-xl p-6">
              <h4 className="text-base font-semibold text-gray-800 mb-4 flex items-center">
                <div className="h-8 w-8 bg-eco-100 rounded-lg flex items-center justify-center mr-3">
                  <Calculator className="h-4 w-4 text-eco-600" />
                </div>
                Estimated Earnings {estimatedEarnings.range ?
                  `(Average ROI: ${formatNumber((estimatedEarnings.range.dailyReturnMin + estimatedEarnings.range.dailyReturnMax) / 2, 1)}%)` :
                  ''
                }
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="bg-white rounded-xl p-4 shadow-sm">
                    <div className="text-2xl font-bold text-eco-600 mb-1">
                      {formatCurrency(estimatedEarnings.daily)}
                    </div>
                    <div className="text-sm text-gray-600 font-medium">Daily</div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="bg-white rounded-xl p-4 shadow-sm">
                    <div className="text-2xl font-bold text-eco-600 mb-1">
                      {formatCurrency(estimatedEarnings.weekly)}
                    </div>
                    <div className="text-sm text-gray-600 font-medium">Weekly</div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="bg-white rounded-xl p-4 shadow-sm">
                    <div className="text-2xl font-bold text-eco-600 mb-1">
                      {formatCurrency(estimatedEarnings.monthly)}
                    </div>
                    <div className="text-sm text-gray-600 font-medium">Monthly</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Important Notes */}
          <div className="bg-gray-50 rounded-xl p-6">
            <h4 className="text-base font-semibold text-gray-800 mb-4">Important Notes:</h4>
            <ul className="text-sm text-gray-600 space-y-2">
              <li className="flex items-start">
                <span className="text-solar-500 mr-2">•</span>
                <span>Minimum purchase: ${minPurchaseAmount}</span>
              </li>
              <li className="flex items-start">
                <span className="text-solar-500 mr-2">•</span>
                <span>Mining units are active for 24 months</span>
              </li>
              <li className="flex items-start">
                <span className="text-solar-500 mr-2">•</span>
                <span>Units expire when 5x investment is earned</span>
              </li>
              <li className="flex items-start">
                <span className="text-solar-500 mr-2">•</span>
                <span>Weekly payouts every Saturday at 15:00 UTC</span>
              </li>
              <li className="flex items-start">
                <span className="text-solar-500 mr-2">•</span>
                <span>ROI varies daily based on mining performance</span>
              </li>
            </ul>
          </div>

          <Button
            type="submit"
            size="lg"
            className="w-full h-14 text-lg font-semibold rounded-xl"
            loading={loading}
            disabled={!formData.thsAmount || !formData.investmentAmount || parseFloat(formData.investmentAmount) < minPurchaseAmount}
          >
            <Cryptocurrency className="h-6 w-6 mr-3" />
            Purchase Mining Unit
          </Button>
        </form>

        {/* Dialog Components */}
        <ConfirmDialog />
        <MessageBoxComponent />
      </CardContent>
    </Card>
  );
};
