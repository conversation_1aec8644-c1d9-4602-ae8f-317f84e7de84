{"name": "hashcorex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:referral": "jest src/tests/referral.test.ts", "test:integration": "jest src/tests/integration.test.ts", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.10.1", "@types/bcryptjs": "^2.4.6", "@types/d3-hierarchy": "^3.1.7", "@types/d3-scale": "^4.0.9", "@types/d3-selection": "^3.0.11", "@types/d3-transition": "^3.0.9", "@types/d3-zoom": "^3.0.8", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "@xyflow/react": "^12.7.0", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3-hierarchy": "^3.1.2", "d3-scale": "^4.0.2", "d3-selection": "^3.0.0", "d3-transition": "^3.0.1", "d3-zoom": "^3.0.0", "handlebars": "^4.7.8", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.518.0", "next": "15.3.4", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "tron-format-address": "^0.1.12", "tsx": "^4.20.3", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.2", "jest-environment-node": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5"}}