const CHUNK_PUBLIC_PATH = "server/app/api/admin/withdrawals/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_referral_ts_916e93f5._.js");
runtime.loadChunk("server/chunks/node_modules_dd67fc79._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__c3130f41._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/withdrawals/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/withdrawals/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/withdrawals/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
