'use client';

import React from 'react';
import { createPortal } from 'react-dom';
import { AlertTriangle, CheckCircle, Info, X, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from './Button';

export interface MessageBoxProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string | React.ReactNode;
  variant?: 'info' | 'success' | 'warning' | 'error';
  darkMode?: boolean;
  showCloseButton?: boolean;
  buttonText?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const MessageBox: React.FC<MessageBoxProps> = ({
  isOpen,
  onClose,
  title,
  message,
  variant = 'info',
  darkMode = false,
  showCloseButton = true,
  buttonText = 'OK',
  size = 'md',
}) => {
  if (!isOpen) return null;

  const getIcon = () => {
    switch (variant) {
      case 'error':
        return <AlertCircle className="h-6 w-6 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-6 w-6 text-yellow-500" />;
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      default:
        return <Info className="h-6 w-6 text-blue-500" />;
    }
  };

  const getButtonVariant = () => {
    switch (variant) {
      case 'error':
        return 'danger';
      case 'warning':
        return 'warning';
      case 'success':
        return 'success';
      default:
        return 'primary';
    }
  };

  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
  };

  const modalContent = (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
      
      {/* Message Box */}
      <div
        className={cn(
          'relative w-full rounded-xl shadow-xl transform transition-all',
          darkMode ? 'bg-slate-800 border border-slate-700' : 'bg-white',
          sizeClasses[size]
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className={cn(
          'flex items-center justify-between p-6 border-b',
          darkMode ? 'border-slate-700' : 'border-gray-200'
        )}>
          <div className="flex items-center space-x-3">
            {getIcon()}
            <h2 className={cn(
              'text-lg font-semibold',
              darkMode ? 'text-white' : 'text-gray-900'
            )}>
              {title}
            </h2>
          </div>
          {showCloseButton && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        {/* Content */}
        <div className="p-6">
          {typeof message === 'string' ? (
            <p className={cn(
              'text-sm leading-relaxed',
              darkMode ? 'text-slate-300' : 'text-gray-600'
            )}>
              {message}
            </p>
          ) : (
            <div className={cn(
              'text-sm leading-relaxed',
              darkMode ? 'text-slate-300' : 'text-gray-600'
            )}>
              {message}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className={cn(
          'flex items-center justify-end p-6 border-t',
          darkMode ? 'border-slate-700' : 'border-gray-200'
        )}>
          <Button
            variant={getButtonVariant()}
            onClick={onClose}
            className="min-w-[80px]"
          >
            {buttonText}
          </Button>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

// Hook for easier usage
export const useMessageBox = () => {
  const [messageState, setMessageState] = React.useState<{
    isOpen: boolean;
    title: string;
    message: string | React.ReactNode;
    variant?: 'info' | 'success' | 'warning' | 'error';
    darkMode?: boolean;
    showCloseButton?: boolean;
    buttonText?: string;
    size?: 'sm' | 'md' | 'lg';
  }>({
    isOpen: false,
    title: '',
    message: '',
  });

  const showMessage = (options: {
    title: string;
    message: string | React.ReactNode;
    variant?: 'info' | 'success' | 'warning' | 'error';
    darkMode?: boolean;
    showCloseButton?: boolean;
    buttonText?: string;
    size?: 'sm' | 'md' | 'lg';
  }) => {
    setMessageState({
      isOpen: true,
      ...options,
    });
  };

  const hideMessage = () => {
    setMessageState(prev => ({ ...prev, isOpen: false }));
  };

  const MessageBoxComponent = () => (
    <MessageBox
      {...messageState}
      onClose={hideMessage}
    />
  );

  return {
    showMessage,
    hideMessage,
    MessageBoxComponent,
  };
};
