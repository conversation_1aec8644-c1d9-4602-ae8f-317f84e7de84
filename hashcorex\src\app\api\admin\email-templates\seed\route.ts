import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyAdminAuth } from '@/lib/auth';
import { defaultEmailTemplates } from '@/lib/emailTemplates';

// POST - Seed default email templates
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ success: false, message: authResult.message }, { status: 401 });
    }

    let createdCount = 0;
    let updatedCount = 0;

    for (const template of defaultEmailTemplates) {
      const existingTemplate = await prisma.emailTemplate.findFirst({
        where: { name: template.name }
      });

      if (existingTemplate) {
        // Update existing template
        await prisma.emailTemplate.update({
          where: { id: existingTemplate.id },
          data: {
            subject: template.subject,
            htmlContent: template.htmlContent,
            textContent: template.textContent,
            variables: template.variables,
            isActive: true,
          }
        });
        updatedCount++;
      } else {
        // Create new template
        await prisma.emailTemplate.create({
          data: {
            name: template.name,
            subject: template.subject,
            htmlContent: template.htmlContent,
            textContent: template.textContent,
            variables: template.variables,
            isActive: true,
          }
        });
        createdCount++;
      }
    }

    return NextResponse.json({
      success: true,
      message: `Email templates seeded successfully. Created: ${createdCount}, Updated: ${updatedCount}`,
      data: {
        created: createdCount,
        updated: updatedCount,
        total: defaultEmailTemplates.length
      }
    });
  } catch (error) {
    console.error('Error seeding email templates:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to seed email templates' },
      { status: 500 }
    );
  }
}
