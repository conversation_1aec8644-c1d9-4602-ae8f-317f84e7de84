import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { systemLogDb } from '@/lib/database';
import { prisma } from '@/lib/prisma';
import { emailHelpers } from '@/lib/email';

// POST - Review KYC documents (approve/reject)
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { userId, action, rejectionReason } = body;

    // Validation
    if (!userId || !action || !['APPROVE', 'REJECT'].includes(action)) {
      return NextResponse.json(
        { success: false, error: 'Invalid request parameters' },
        { status: 400 }
      );
    }

    if (action === 'REJECT' && !rejectionReason) {
      return NextResponse.json(
        { success: false, error: 'Rejection reason is required' },
        { status: 400 }
      );
    }

    // Get user's pending KYC documents
    const userDocuments = await prisma.kYCDocument.findMany({
      where: {
        userId,
        status: 'PENDING',
      },
    });

    if (userDocuments.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No pending documents found for this user' },
        { status: 404 }
      );
    }

    const newStatus = action === 'APPROVE' ? 'APPROVED' : 'REJECTED';
    const reviewedAt = new Date();

    // Update all user's documents
    await prisma.kYCDocument.updateMany({
      where: {
        userId,
        status: 'PENDING',
      },
      data: {
        status: newStatus,
        reviewedAt,
        reviewedBy: user.email,
        rejectionReason: action === 'REJECT' ? rejectionReason : null,
      },
    });

    // Update user's KYC status and get user details for email
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { kycStatus: newStatus },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
      }
    });

    // Send email notification
    if (action === 'APPROVE') {
      emailHelpers.sendKYCApproved({
        email: updatedUser.email,
        firstName: updatedUser.firstName
      }).catch(error => {
        console.error('Failed to send KYC approval email:', error);
      });
    } else if (action === 'REJECT') {
      emailHelpers.sendKYCRejected({
        email: updatedUser.email,
        firstName: updatedUser.firstName
      }, rejectionReason || 'Documents did not meet verification requirements').catch(error => {
        console.error('Failed to send KYC rejection email:', error);
      });
    }

    // Log the review action
    await systemLogDb.create({
      action: 'KYC_REVIEWED',
      adminId: user.id,
      details: {
        reviewedUserId: userId,
        action,
        rejectionReason: action === 'REJECT' ? rejectionReason : null,
        documentsCount: userDocuments.length,
        reviewedBy: user.email,
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: `KYC ${action.toLowerCase()}d successfully`,
      data: {
        userId,
        status: newStatus,
        reviewedAt,
        reviewedBy: user.email,
      },
    });

  } catch (error: any) {
    console.error('KYC review error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to review KYC documents' },
      { status: 500 }
    );
  }
}
