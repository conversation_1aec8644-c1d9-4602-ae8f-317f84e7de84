import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { emailService } from '@/lib/email';
import { verifyAdminAuth } from '@/lib/auth';

// GET - Get current SMTP configuration
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ success: false, message: authResult.message }, { status: 401 });
    }

    const smtpConfig = await prisma.smtpConfiguration.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        host: true,
        port: true,
        secure: true,
        username: true,
        senderName: true,
        senderEmail: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        // Don't return password for security
      }
    });

    return NextResponse.json({
      success: true,
      data: smtpConfig
    });
  } catch (error) {
    console.error('Error fetching SMTP configuration:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch SMTP configuration' },
      { status: 500 }
    );
  }
}

// POST - Create or update SMTP configuration
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ success: false, message: authResult.message }, { status: 401 });
    }

    const body = await request.json();
    const { host, port, secure, username, password, senderName, senderEmail } = body;

    // Validate required fields
    if (!host || !port || !username || !password || !senderName || !senderEmail) {
      return NextResponse.json(
        { success: false, message: 'All SMTP fields are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(senderEmail)) {
      return NextResponse.json(
        { success: false, message: 'Invalid sender email format' },
        { status: 400 }
      );
    }

    // Validate port number
    if (isNaN(port) || port < 1 || port > 65535) {
      return NextResponse.json(
        { success: false, message: 'Port must be a number between 1 and 65535' },
        { status: 400 }
      );
    }

    // Deactivate existing configurations
    await prisma.smtpConfiguration.updateMany({
      where: { isActive: true },
      data: { isActive: false }
    });

    // Create new configuration
    const newConfig = await prisma.smtpConfiguration.create({
      data: {
        host,
        port: parseInt(port),
        secure: Boolean(secure),
        username,
        password,
        senderName,
        senderEmail,
        isActive: true,
      },
      select: {
        id: true,
        host: true,
        port: true,
        secure: true,
        username: true,
        senderName: true,
        senderEmail: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    return NextResponse.json({
      success: true,
      message: 'SMTP configuration saved successfully',
      data: newConfig
    });
  } catch (error) {
    console.error('Error saving SMTP configuration:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to save SMTP configuration' },
      { status: 500 }
    );
  }
}

// PUT - Test SMTP connection
export async function PUT(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ success: false, message: authResult.message }, { status: 401 });
    }

    const body = await request.json();
    const { testEmail } = body;

    if (!testEmail) {
      return NextResponse.json(
        { success: false, message: 'Test email address is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(testEmail)) {
      return NextResponse.json(
        { success: false, message: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Test connection first
    const connectionResult = await emailService.testConnection();
    if (!connectionResult.success) {
      return NextResponse.json({
        success: false,
        message: `Connection failed: ${connectionResult.message}`
      }, { status: 400 });
    }

    // Send test email
    const testResult = await emailService.sendTestEmail(testEmail);
    
    return NextResponse.json({
      success: testResult.success,
      message: testResult.message
    });
  } catch (error) {
    console.error('Error testing SMTP configuration:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to test SMTP configuration' },
      { status: 500 }
    );
  }
}
