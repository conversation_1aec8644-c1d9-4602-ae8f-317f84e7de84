import { NextRequest, NextResponse } from 'next/server';
import { processWeeklyEarnings } from '@/lib/mining';
import { systemLogDb } from '@/lib/database';

// This endpoint should be called weekly on Saturday at 18:30 UTC (Sunday 00:00 GMT+5:30)
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (cron service)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'default-secret';
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Starting weekly earnings payout cron job...');

    // Process weekly earnings distribution
    const payoutResults = await processWeeklyEarnings();
    console.log(`Processed earnings for ${payoutResults.length} users`);

    const totalDistributed = payoutResults.reduce((sum, result) => sum + result.totalEarnings, 0);

    // Log the cron job execution
    await systemLogDb.create({
      action: 'WEEKLY_PAYOUT_CRON_EXECUTED',
      details: {
        usersProcessed: payoutResults.length,
        totalDistributed,
        executionTime: new Date().toISOString(),
        payoutDay: 'Saturday',
        payoutTime: '18:30 UTC (Sunday 00:00 GMT+5:30)',
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Weekly earnings payout completed',
      data: {
        usersProcessed: payoutResults.length,
        totalDistributed,
      },
    });

  } catch (error: any) {
    console.error('Weekly payout cron job error:', error);

    // Log the error
    await systemLogDb.create({
      action: 'WEEKLY_PAYOUT_CRON_ERROR',
      details: {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json(
      { success: false, error: 'Weekly payout failed' },
      { status: 500 }
    );
  }
}
