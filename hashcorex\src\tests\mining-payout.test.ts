import { calculateDailyROI, processWeeklyEarnings } from '@/lib/mining';
import { prisma } from '@/lib/prisma';
import { walletBalanceDb, transactionDb, miningUnitDb } from '@/lib/database';

describe('Mining Earnings Payout System', () => {
  let testUserId: string;
  let testMiningUnitId: string;

  beforeAll(async () => {
    // Create test user
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        password: 'hashedpassword',
        referralId: 'TEST-MINING-001',
      },
    });
    testUserId = testUser.id;

    // Create test mining unit
    const testMiningUnit = await prisma.miningUnit.create({
      data: {
        userId: testUserId,
        thsAmount: 100,
        investmentAmount: 1000,
        dailyROI: 0.5, // 0.5% daily
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      },
    });
    testMiningUnitId = testMiningUnit.id;

    // Initialize wallet balance
    await walletBalanceDb.getOrCreate(testUserId);
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.miningUnit.deleteMany({ where: { userId: testUserId } });
    await prisma.transaction.deleteMany({ where: { userId: testUserId } });
    await prisma.walletBalance.deleteMany({ where: { userId: testUserId } });
    await prisma.user.delete({ where: { id: testUserId } });
  });

  test('Daily ROI calculation creates pending transactions', async () => {
    // Calculate daily ROI
    const results = await calculateDailyROI();
    
    // Find our test user's result
    const userResult = results.find(r => r.userId === testUserId);
    expect(userResult).toBeDefined();
    expect(userResult?.totalEarnings).toBe(5); // 1000 * 0.5% = 5

    // Check that pending transaction was created
    const pendingTransactions = await prisma.transaction.findMany({
      where: {
        userId: testUserId,
        type: 'MINING_EARNINGS',
        status: 'PENDING',
      },
    });

    expect(pendingTransactions.length).toBeGreaterThan(0);
    expect(pendingTransactions[0].amount).toBe(5);
  });

  test('Weekly payout processes pending earnings and credits wallet', async () => {
    // Get initial wallet balance
    const initialWallet = await walletBalanceDb.getOrCreate(testUserId);
    const initialBalance = initialWallet.availableBalance;

    // Process weekly earnings
    const payoutResults = await processWeeklyEarnings();
    
    // Find our test user's result
    const userPayout = payoutResults.find(r => r.userId === testUserId);
    expect(userPayout).toBeDefined();

    // Check that transactions are now completed
    const completedTransactions = await prisma.transaction.findMany({
      where: {
        userId: testUserId,
        type: 'MINING_EARNINGS',
        status: 'COMPLETED',
      },
    });

    expect(completedTransactions.length).toBeGreaterThan(0);

    // Check that no pending transactions remain
    const pendingTransactions = await prisma.transaction.findMany({
      where: {
        userId: testUserId,
        type: 'MINING_EARNINGS',
        status: 'PENDING',
      },
    });

    expect(pendingTransactions.length).toBe(0);

    // Check that wallet balance was credited
    const updatedWallet = await walletBalanceDb.getOrCreate(testUserId);
    expect(updatedWallet.availableBalance).toBeGreaterThan(initialBalance);
    expect(updatedWallet.totalEarnings).toBeGreaterThan(0);
  });

  test('Pending earnings are shown correctly in wallet balance API', async () => {
    // Create another pending transaction for testing
    await transactionDb.create({
      userId: testUserId,
      type: 'MINING_EARNINGS',
      amount: 10,
      description: 'Test pending earnings',
      status: 'PENDING',
    });

    // Get all transactions for balance calculation
    const allTransactions = await prisma.transaction.findMany({
      where: { userId: testUserId },
    });

    // Calculate pending earnings
    const pendingEarnings = allTransactions
      .filter(t => t.status === 'PENDING' && t.type === 'MINING_EARNINGS')
      .reduce((sum, t) => sum + t.amount, 0);

    expect(pendingEarnings).toBeGreaterThan(0);
  });
});
