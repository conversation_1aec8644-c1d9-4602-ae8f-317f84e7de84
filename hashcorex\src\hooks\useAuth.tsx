'use client';

import React, { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { User } from '@/types';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, firstName: string, lastName: string, password: string, confirmPassword: string, referralCode?: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Helper function to get cached user from localStorage
  const getCachedUser = (): User | null => {
    if (typeof window === 'undefined') return null;
    try {
      const cached = localStorage.getItem('auth-user');
      return cached ? JSON.parse(cached) : null;
    } catch {
      return null;
    }
  };

  // Helper function to cache user in localStorage
  const setCachedUser = (userData: User | null) => {
    if (typeof window === 'undefined') return;
    try {
      if (userData) {
        localStorage.setItem('auth-user', JSON.stringify(userData));
      } else {
        localStorage.removeItem('auth-user');
      }
    } catch {
      // Ignore localStorage errors
    }
  };

  // Initialize authentication state
  useEffect(() => {
    // First, try to load cached user to provide immediate feedback
    const cachedUser = getCachedUser();
    if (cachedUser) {
      setUser(cachedUser);
      setLoading(false);
    }

    // Then verify with server
    checkAuth();

    // Listen for page visibility changes (handles browser back/forward)
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isInitialized) {
        // Re-check authentication when page becomes visible
        checkAuth();
      }
    };

    // Listen for focus events (handles browser back/forward)
    const handleFocus = () => {
      if (isInitialized) {
        checkAuth();
      }
    };

    // Listen for storage changes (handles logout in other tabs)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth-user') {
        const newUser = e.newValue ? JSON.parse(e.newValue) : null;
        setUser(newUser);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('storage', handleStorageChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [isInitialized]);

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUser(data.data);
        } else {
          // Clear user if authentication failed
          setUser(null);
        }
      } else {
        // Clear user if request failed
        setUser(null);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      // Clear user on error
      setUser(null);
    } finally {
      setLoading(false);
      setIsInitialized(true);
    }
  };

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Login failed');
      }

      setUser(data.data.user);
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (
    email: string,
    firstName: string,
    lastName: string,
    password: string,
    confirmPassword: string,
    referralCode?: string,
    placementSide?: 'left' | 'right'
  ) => {
    setLoading(true);
    try {
      // Build URL with side parameter if provided
      const url = placementSide
        ? `/api/auth/register?side=${placementSide}`
        : '/api/auth/register';

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          firstName,
          lastName,
          password,
          confirmPassword,
          referralCode,
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Registration failed');
      }

      // Auto-login after successful registration
      await login(email, password);
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
    }
  };

  const refreshUser = async () => {
    await checkAuth();
  };

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
