export const defaultEmailTemplates = [
  {
    name: 'welcome',
    subject: 'Welcome to HashCoreX - Your Mining Journey Begins!',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Welcome to HashCoreX</h1>
          <p style="color: #dcfce7; margin: 10px 0 0 0; font-size: 16px;">Your Mining Journey Begins Now!</p>
        </div>
        
        <div style="padding: 40px 20px; background-color: white;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Hello {{firstName}}!</h2>
          
          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
            Welcome to HashCoreX, the premier Bitcoin mining platform. We're excited to have you join our community of miners!
          </p>
          
          <div style="background-color: #f0fdf4; border-left: 4px solid #10b981; padding: 20px; margin: 20px 0;">
            <h3 style="color: #059669; margin: 0 0 10px 0;">Your Account Details:</h3>
            <p style="margin: 5px 0; color: #374151;"><strong>Email:</strong> {{email}}</p>
            <p style="margin: 5px 0; color: #374151;"><strong>Referral ID:</strong> {{referralId}}</p>
            <p style="margin: 5px 0; color: #374151;"><strong>Registration Date:</strong> {{registrationDate}}</p>
          </div>
          
          <h3 style="color: #1f2937; margin: 30px 0 15px 0;">Next Steps:</h3>
          <ul style="color: #4b5563; line-height: 1.8; padding-left: 20px;">
            <li>Complete your KYC verification for full account access</li>
            <li>Make your first deposit to start mining</li>
            <li>Purchase mining units to begin earning daily returns</li>
            <li>Refer friends and earn commission bonuses</li>
          </ul>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background-color: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
              Access Your Dashboard
            </a>
          </div>
          
          <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
            If you have any questions, our support team is here to help. Contact us anytime!
          </p>
        </div>
        
        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
          <p style="margin: 5px 0 0 0;">This email was sent to {{email}}</p>
        </div>
      </div>
    `,
    textContent: `Welcome to HashCoreX!

Hello {{firstName}},

Welcome to HashCoreX, the premier Bitcoin mining platform. We're excited to have you join our community of miners!

Your Account Details:
- Email: {{email}}
- Referral ID: {{referralId}}
- Registration Date: {{registrationDate}}

Next Steps:
1. Complete your KYC verification for full account access
2. Make your first deposit to start mining
3. Purchase mining units to begin earning daily returns
4. Refer friends and earn commission bonuses

Access your dashboard: {{dashboardUrl}}

If you have any questions, our support team is here to help. Contact us anytime!

© 2024 HashCoreX. All rights reserved.`,
    variables: ['firstName', 'email', 'referralId', 'registrationDate', 'dashboardUrl']
  },
  
  {
    name: 'email-verification',
    subject: 'Verify Your Email - HashCoreX',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">Email Verification</h1>
        </div>
        
        <div style="padding: 40px 20px; background-color: white; text-align: center;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Verify Your Email Address</h2>
          
          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 30px;">
            Please use the verification code below to verify your email address:
          </p>
          
          <div style="background-color: #f3f4f6; border: 2px dashed #d1d5db; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <div style="font-size: 32px; font-weight: bold; color: #1f2937; letter-spacing: 8px; font-family: monospace;">
              {{verificationCode}}
            </div>
          </div>
          
          <p style="color: #6b7280; font-size: 14px; margin-top: 20px;">
            This code will expire in 10 minutes. If you didn't request this verification, please ignore this email.
          </p>
        </div>
        
        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `,
    textContent: `Email Verification - HashCoreX

Verify Your Email Address

Please use the verification code below to verify your email address:

{{verificationCode}}

This code will expire in 10 minutes. If you didn't request this verification, please ignore this email.

© 2024 HashCoreX. All rights reserved.`,
    variables: ['verificationCode']
  },
  
  {
    name: 'password-reset',
    subject: 'Reset Your Password - HashCoreX',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">Password Reset</h1>
        </div>
        
        <div style="padding: 40px 20px; background-color: white; text-align: center;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Reset Your Password</h2>
          
          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 30px;">
            You requested a password reset. Use the code below to reset your password:
          </p>
          
          <div style="background-color: #fef3c7; border: 2px dashed #f59e0b; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <div style="font-size: 32px; font-weight: bold; color: #92400e; letter-spacing: 8px; font-family: monospace;">
              {{resetCode}}
            </div>
          </div>
          
          <p style="color: #6b7280; font-size: 14px; margin-top: 20px;">
            This code will expire in 15 minutes. If you didn't request a password reset, please ignore this email.
          </p>
        </div>
        
        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `,
    textContent: `Password Reset - HashCoreX

Reset Your Password

You requested a password reset. Use the code below to reset your password:

{{resetCode}}

This code will expire in 15 minutes. If you didn't request a password reset, please ignore this email.

© 2024 HashCoreX. All rights reserved.`,
    variables: ['resetCode']
  },

  {
    name: 'kyc-approved',
    subject: 'KYC Verification Approved - HashCoreX',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">KYC Verification Approved</h1>
        </div>

        <div style="padding: 40px 20px; background-color: white;">
          <div style="text-align: center; margin-bottom: 30px;">
            <div style="width: 80px; height: 80px; background-color: #10b981; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
              <span style="color: white; font-size: 40px;">✓</span>
            </div>
            <h2 style="color: #1f2937; margin: 0;">Congratulations {{firstName}}!</h2>
          </div>

          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
            Your KYC verification has been successfully approved. You now have full access to all HashCoreX features including:
          </p>

          <ul style="color: #4b5563; line-height: 1.8; padding-left: 20px; margin-bottom: 30px;">
            <li>Unlimited deposits and withdrawals</li>
            <li>Higher mining unit purchase limits</li>
            <li>Priority customer support</li>
            <li>Access to premium features</li>
          </ul>

          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background-color: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
              Start Mining Now
            </a>
          </div>
        </div>

        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `,
    textContent: `KYC Verification Approved - HashCoreX

Congratulations {{firstName}}!

Your KYC verification has been successfully approved. You now have full access to all HashCoreX features including:

- Unlimited deposits and withdrawals
- Higher mining unit purchase limits
- Priority customer support
- Access to premium features

Start mining now: {{dashboardUrl}}

© 2024 HashCoreX. All rights reserved.`,
    variables: ['firstName', 'dashboardUrl']
  },

  {
    name: 'kyc-rejected',
    subject: 'KYC Verification Update - HashCoreX',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">KYC Verification Update</h1>
        </div>

        <div style="padding: 40px 20px; background-color: white;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Hello {{firstName}},</h2>

          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
            We've reviewed your KYC verification documents, and unfortunately, we need additional information to complete the verification process.
          </p>

          <div style="background-color: #fef2f2; border-left: 4px solid #ef4444; padding: 20px; margin: 20px 0;">
            <h3 style="color: #dc2626; margin: 0 0 10px 0;">Reason for Review:</h3>
            <p style="margin: 0; color: #374151;">{{rejectionReason}}</p>
          </div>

          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 30px;">
            Please resubmit your KYC documents with the required corrections. Our team will review your submission within 24-48 hours.
          </p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="{{kycUrl}}" style="background-color: #ef4444; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
              Resubmit KYC Documents
            </a>
          </div>
        </div>

        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `,
    textContent: `KYC Verification Update - HashCoreX

Hello {{firstName}},

We've reviewed your KYC verification documents, and unfortunately, we need additional information to complete the verification process.

Reason for Review:
{{rejectionReason}}

Please resubmit your KYC documents with the required corrections. Our team will review your submission within 24-48 hours.

Resubmit KYC documents: {{kycUrl}}

© 2024 HashCoreX. All rights reserved.`,
    variables: ['firstName', 'rejectionReason', 'kycUrl']
  },

  {
    name: 'withdrawal-approved',
    subject: 'Withdrawal Approved - HashCoreX',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">Withdrawal Approved</h1>
        </div>

        <div style="padding: 40px 20px; background-color: white;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Hello {{firstName}},</h2>

          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
            Your withdrawal request has been approved and processed successfully.
          </p>

          <div style="background-color: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #059669; margin: 0 0 15px 0;">Withdrawal Details:</h3>
            <table style="width: 100%; color: #374151;">
              <tr><td style="padding: 5px 0;"><strong>Amount:</strong></td><td style="text-align: right;">${{amount}} USDT</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Fee:</strong></td><td style="text-align: right;">${{fee}} USDT</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Net Amount:</strong></td><td style="text-align: right;">${{netAmount}} USDT</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Wallet Address:</strong></td><td style="text-align: right; font-family: monospace; font-size: 12px;">{{walletAddress}}</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Transaction ID:</strong></td><td style="text-align: right; font-family: monospace; font-size: 12px;">{{transactionId}}</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Processed Date:</strong></td><td style="text-align: right;">{{processedDate}}</td></tr>
            </table>
          </div>

          <p style="color: #6b7280; font-size: 14px; margin-top: 20px;">
            The funds should appear in your wallet within 10-30 minutes depending on network congestion.
          </p>
        </div>

        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `,
    textContent: `Withdrawal Approved - HashCoreX

Hello {{firstName}},

Your withdrawal request has been approved and processed successfully.

Withdrawal Details:
- Amount: ${{amount}} USDT
- Fee: ${{fee}} USDT
- Net Amount: ${{netAmount}} USDT
- Wallet Address: {{walletAddress}}
- Transaction ID: {{transactionId}}
- Processed Date: {{processedDate}}

The funds should appear in your wallet within 10-30 minutes depending on network congestion.

© 2024 HashCoreX. All rights reserved.`,
    variables: ['firstName', 'amount', 'fee', 'netAmount', 'walletAddress', 'transactionId', 'processedDate']
  },

  {
    name: 'withdrawal-rejected',
    subject: 'Withdrawal Request Update - HashCoreX',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">Withdrawal Request Update</h1>
        </div>

        <div style="padding: 40px 20px; background-color: white;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Hello {{firstName}},</h2>

          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
            We regret to inform you that your withdrawal request could not be processed at this time.
          </p>

          <div style="background-color: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #dc2626; margin: 0 0 15px 0;">Withdrawal Details:</h3>
            <table style="width: 100%; color: #374151; margin-bottom: 15px;">
              <tr><td style="padding: 5px 0;"><strong>Amount:</strong></td><td style="text-align: right;">${{amount}} USDT</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Requested Date:</strong></td><td style="text-align: right;">{{requestedDate}}</td></tr>
            </table>
            <h4 style="color: #dc2626; margin: 15px 0 5px 0;">Reason:</h4>
            <p style="margin: 0; color: #374151;">{{rejectionReason}}</p>
          </div>

          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
            The requested amount has been returned to your wallet balance. You can submit a new withdrawal request after addressing the issue mentioned above.
          </p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background-color: #ef4444; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
              View Dashboard
            </a>
          </div>
        </div>

        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `,
    textContent: `Withdrawal Request Update - HashCoreX

Hello {{firstName}},

We regret to inform you that your withdrawal request could not be processed at this time.

Withdrawal Details:
- Amount: ${{amount}} USDT
- Requested Date: {{requestedDate}}

Reason: {{rejectionReason}}

The requested amount has been returned to your wallet balance. You can submit a new withdrawal request after addressing the issue mentioned above.

View Dashboard: {{dashboardUrl}}

© 2024 HashCoreX. All rights reserved.`,
    variables: ['firstName', 'amount', 'requestedDate', 'rejectionReason', 'dashboardUrl']
  },

  {
    name: 'mining-purchase',
    subject: 'Mining Unit Purchase Confirmation - HashCoreX',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">Mining Unit Purchase Confirmed</h1>
        </div>

        <div style="padding: 40px 20px; background-color: white;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Hello {{firstName}},</h2>

          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
            Congratulations! Your mining unit purchase has been confirmed and is now active.
          </p>

          <div style="background-color: #faf5ff; border: 1px solid #e9d5ff; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #7c3aed; margin: 0 0 15px 0;">Mining Unit Details:</h3>
            <table style="width: 100%; color: #374151;">
              <tr><td style="padding: 5px 0;"><strong>Hash Power:</strong></td><td style="text-align: right;">{{thsAmount}} TH/s</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Investment Amount:</strong></td><td style="text-align: right;">${{investmentAmount}} USDT</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Daily ROI:</strong></td><td style="text-align: right;">{{dailyROI}}%</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Expected Daily Earnings:</strong></td><td style="text-align: right;">${{dailyEarnings}} USDT</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Start Date:</strong></td><td style="text-align: right;">{{startDate}}</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Expiry Date:</strong></td><td style="text-align: right;">{{expiryDate}}</td></tr>
            </table>
          </div>

          <div style="background-color: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 20px; margin: 20px 0;">
            <h4 style="color: #0369a1; margin: 0 0 10px 0;">Important Information:</h4>
            <ul style="color: #374151; margin: 0; padding-left: 20px;">
              <li>Daily earnings will be credited every day</li>
              <li>Weekly payouts occur every Sunday at 00:00 GMT+5:30</li>
              <li>Mining unit expires after reaching 5x return or 24 months</li>
            </ul>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background-color: #8b5cf6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
              View Mining Dashboard
            </a>
          </div>
        </div>

        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `,
    textContent: `Mining Unit Purchase Confirmed - HashCoreX

Hello {{firstName}},

Congratulations! Your mining unit purchase has been confirmed and is now active.

Mining Unit Details:
- Hash Power: {{thsAmount}} TH/s
- Investment Amount: ${{investmentAmount}} USDT
- Daily ROI: {{dailyROI}}%
- Expected Daily Earnings: ${{dailyEarnings}} USDT
- Start Date: {{startDate}}
- Expiry Date: {{expiryDate}}

Important Information:
- Daily earnings will be credited every day
- Weekly payouts occur every Sunday at 00:00 GMT+5:30
- Mining unit expires after reaching 5x return or 24 months

View Mining Dashboard: {{dashboardUrl}}

© 2024 HashCoreX. All rights reserved.`,
    variables: ['firstName', 'thsAmount', 'investmentAmount', 'dailyROI', 'dailyEarnings', 'startDate', 'expiryDate', 'dashboardUrl']
  },

  {
    name: 'weekly-earnings',
    subject: 'Weekly Mining Earnings Payout - HashCoreX',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">Weekly Earnings Payout</h1>
        </div>

        <div style="padding: 40px 20px; background-color: white;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Hello {{firstName}},</h2>

          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
            Your weekly mining earnings have been successfully processed and added to your wallet balance.
          </p>

          <div style="background-color: #fffbeb; border: 1px solid #fed7aa; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #d97706; margin: 0 0 15px 0;">Earnings Summary:</h3>
            <table style="width: 100%; color: #374151;">
              <tr><td style="padding: 5px 0;"><strong>Mining Earnings:</strong></td><td style="text-align: right;">${{miningEarnings}} USDT</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Referral Bonuses:</strong></td><td style="text-align: right;">${{referralBonuses}} USDT</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Binary Bonuses:</strong></td><td style="text-align: right;">${{binaryBonuses}} USDT</td></tr>
              <tr style="border-top: 1px solid #fed7aa;"><td style="padding: 10px 0 5px 0;"><strong>Total Payout:</strong></td><td style="text-align: right; font-size: 18px; font-weight: bold; color: #d97706;">${{totalPayout}} USDT</td></tr>
              <tr><td style="padding: 5px 0;"><strong>Payout Date:</strong></td><td style="text-align: right;">{{payoutDate}}</td></tr>
            </table>
          </div>

          <div style="background-color: #f0fdf4; border-left: 4px solid #10b981; padding: 20px; margin: 20px 0;">
            <h4 style="color: #059669; margin: 0 0 10px 0;">Current Wallet Balance:</h4>
            <p style="color: #374151; margin: 0; font-size: 18px; font-weight: bold;">${{walletBalance}} USDT</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background-color: #f59e0b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
              View Wallet
            </a>
          </div>
        </div>

        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `,
    textContent: `Weekly Earnings Payout - HashCoreX

Hello {{firstName}},

Your weekly mining earnings have been successfully processed and added to your wallet balance.

Earnings Summary:
- Mining Earnings: ${{miningEarnings}} USDT
- Referral Bonuses: ${{referralBonuses}} USDT
- Binary Bonuses: ${{binaryBonuses}} USDT
- Total Payout: ${{totalPayout}} USDT
- Payout Date: {{payoutDate}}

Current Wallet Balance: ${{walletBalance}} USDT

View Wallet: {{dashboardUrl}}

© 2024 HashCoreX. All rights reserved.`,
    variables: ['firstName', 'miningEarnings', 'referralBonuses', 'binaryBonuses', 'totalPayout', 'payoutDate', 'walletBalance', 'dashboardUrl']
  }
];
