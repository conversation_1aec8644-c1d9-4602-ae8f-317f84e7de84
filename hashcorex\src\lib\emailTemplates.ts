export const defaultEmailTemplates = [
  {
    name: 'welcome',
    subject: 'Welcome to HashCoreX - Your Mining Journey Begins!',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Welcome to HashCoreX</h1>
          <p style="color: #dcfce7; margin: 10px 0 0 0; font-size: 16px;">Your Mining Journey Begins Now!</p>
        </div>
        
        <div style="padding: 40px 20px; background-color: white;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Hello {{firstName}}!</h2>
          
          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
            Welcome to HashCoreX, the premier Bitcoin mining platform. We're excited to have you join our community of miners!
          </p>
          
          <div style="background-color: #f0fdf4; border-left: 4px solid #10b981; padding: 20px; margin: 20px 0;">
            <h3 style="color: #059669; margin: 0 0 10px 0;">Your Account Details:</h3>
            <p style="margin: 5px 0; color: #374151;"><strong>Email:</strong> {{email}}</p>
            <p style="margin: 5px 0; color: #374151;"><strong>Referral ID:</strong> {{referralId}}</p>
            <p style="margin: 5px 0; color: #374151;"><strong>Registration Date:</strong> {{registrationDate}}</p>
          </div>
          
          <h3 style="color: #1f2937; margin: 30px 0 15px 0;">Next Steps:</h3>
          <ul style="color: #4b5563; line-height: 1.8; padding-left: 20px;">
            <li>Complete your KYC verification for full account access</li>
            <li>Make your first deposit to start mining</li>
            <li>Purchase mining units to begin earning daily returns</li>
            <li>Refer friends and earn commission bonuses</li>
          </ul>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background-color: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
              Access Your Dashboard
            </a>
          </div>
          
          <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
            If you have any questions, our support team is here to help. Contact us anytime!
          </p>
        </div>
        
        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
          <p style="margin: 5px 0 0 0;">This email was sent to {{email}}</p>
        </div>
      </div>
    `,
    textContent: `Welcome to HashCoreX!

Hello {{firstName}},

Welcome to HashCoreX, the premier Bitcoin mining platform. We're excited to have you join our community of miners!

Your Account Details:
- Email: {{email}}
- Referral ID: {{referralId}}
- Registration Date: {{registrationDate}}

Next Steps:
1. Complete your KYC verification for full account access
2. Make your first deposit to start mining
3. Purchase mining units to begin earning daily returns
4. Refer friends and earn commission bonuses

Access your dashboard: {{dashboardUrl}}

If you have any questions, our support team is here to help. Contact us anytime!

© 2024 HashCoreX. All rights reserved.`,
    variables: ['firstName', 'email', 'referralId', 'registrationDate', 'dashboardUrl']
  },
  
  {
    name: 'email-verification',
    subject: 'Verify Your Email - HashCoreX',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">Email Verification</h1>
        </div>
        
        <div style="padding: 40px 20px; background-color: white; text-align: center;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Verify Your Email Address</h2>
          
          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 30px;">
            Please use the verification code below to verify your email address:
          </p>
          
          <div style="background-color: #f3f4f6; border: 2px dashed #d1d5db; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <div style="font-size: 32px; font-weight: bold; color: #1f2937; letter-spacing: 8px; font-family: monospace;">
              {{verificationCode}}
            </div>
          </div>
          
          <p style="color: #6b7280; font-size: 14px; margin-top: 20px;">
            This code will expire in 10 minutes. If you didn't request this verification, please ignore this email.
          </p>
        </div>
        
        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `,
    textContent: `Email Verification - HashCoreX

Verify Your Email Address

Please use the verification code below to verify your email address:

{{verificationCode}}

This code will expire in 10 minutes. If you didn't request this verification, please ignore this email.

© 2024 HashCoreX. All rights reserved.`,
    variables: ['verificationCode']
  },
  
  {
    name: 'password-reset',
    subject: 'Reset Your Password - HashCoreX',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8fafc;">
        <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">Password Reset</h1>
        </div>
        
        <div style="padding: 40px 20px; background-color: white; text-align: center;">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Reset Your Password</h2>
          
          <p style="color: #4b5563; line-height: 1.6; margin-bottom: 30px;">
            You requested a password reset. Use the code below to reset your password:
          </p>
          
          <div style="background-color: #fef3c7; border: 2px dashed #f59e0b; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <div style="font-size: 32px; font-weight: bold; color: #92400e; letter-spacing: 8px; font-family: monospace;">
              {{resetCode}}
            </div>
          </div>
          
          <p style="color: #6b7280; font-size: 14px; margin-top: 20px;">
            This code will expire in 15 minutes. If you didn't request a password reset, please ignore this email.
          </p>
        </div>
        
        <div style="background-color: #f1f5f9; padding: 20px; text-align: center; color: #64748b; font-size: 12px;">
          <p style="margin: 0;">© 2024 HashCoreX. All rights reserved.</p>
        </div>
      </div>
    `,
    textContent: `Password Reset - HashCoreX

Reset Your Password

You requested a password reset. Use the code below to reset your password:

{{resetCode}}

This code will expire in 15 minutes. If you didn't request a password reset, please ignore this email.

© 2024 HashCoreX. All rights reserved.`,
    variables: ['resetCode']
  }
];
