import nodemailer from 'nodemailer';
import Handlebars from 'handlebars';
import { prisma } from './prisma';

export interface EmailOptions {
  to: string;
  subject: string;
  template?: string;
  variables?: Record<string, any>;
  html?: string;
  text?: string;
}

export interface SmtpConfig {
  host: string;
  port: number;
  secure: boolean;
  username: string;
  password: string;
  senderName: string;
  senderEmail: string;
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private config: SmtpConfig | null = null;

  async initialize() {
    try {
      // Get active SMTP configuration
      const smtpConfig = await prisma.smtpConfiguration.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' }
      });

      if (!smtpConfig) {
        console.warn('No active SMTP configuration found');
        return false;
      }

      this.config = {
        host: smtpConfig.host,
        port: smtpConfig.port,
        secure: smtpConfig.secure,
        username: smtpConfig.username,
        password: smtpConfig.password,
        senderName: smtpConfig.senderName,
        senderEmail: smtpConfig.senderEmail,
      };

      // Create transporter
      this.transporter = nodemailer.createTransporter({
        host: this.config.host,
        port: this.config.port,
        secure: this.config.secure,
        auth: {
          user: this.config.username,
          pass: this.config.password,
        },
      });

      // Verify connection
      await this.transporter.verify();
      console.log('Email service initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize email service:', error);
      return false;
    }
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      if (!this.transporter || !this.config) {
        const initialized = await this.initialize();
        if (!initialized) {
          throw new Error('Email service not properly configured');
        }
      }

      let htmlContent = options.html;
      let textContent = options.text;

      // If template is specified, load and compile it
      if (options.template) {
        const template = await prisma.emailTemplate.findFirst({
          where: { 
            name: options.template,
            isActive: true 
          }
        });

        if (!template) {
          throw new Error(`Email template '${options.template}' not found`);
        }

        // Compile template with variables
        const htmlTemplate = Handlebars.compile(template.htmlContent);
        const textTemplate = template.textContent ? Handlebars.compile(template.textContent) : null;

        htmlContent = htmlTemplate(options.variables || {});
        textContent = textTemplate ? textTemplate(options.variables || {}) : undefined;

        // Use template subject if not provided
        if (!options.subject && template.subject) {
          const subjectTemplate = Handlebars.compile(template.subject);
          options.subject = subjectTemplate(options.variables || {});
        }
      }

      // Send email
      const result = await this.transporter!.sendMail({
        from: `"${this.config!.senderName}" <${this.config!.senderEmail}>`,
        to: options.to,
        subject: options.subject,
        html: htmlContent,
        text: textContent,
      });

      // Log successful email
      await this.logEmail({
        recipient: options.to,
        subject: options.subject,
        templateId: options.template ? (await this.getTemplateId(options.template)) : null,
        status: 'SENT',
        sentAt: new Date(),
      });

      console.log('Email sent successfully:', result.messageId);
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);

      // Log failed email
      await this.logEmail({
        recipient: options.to,
        subject: options.subject,
        templateId: options.template ? (await this.getTemplateId(options.template)) : null,
        status: 'FAILED',
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      });

      return false;
    }
  }

  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const initialized = await this.initialize();
      if (!initialized) {
        return { success: false, message: 'Failed to initialize email service' };
      }

      await this.transporter!.verify();
      return { success: true, message: 'SMTP connection successful' };
    } catch (error) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 'Connection failed' 
      };
    }
  }

  async sendTestEmail(to: string): Promise<{ success: boolean; message: string }> {
    try {
      const success = await this.sendEmail({
        to,
        subject: 'HashCoreX SMTP Test Email',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #10b981;">HashCoreX SMTP Test</h2>
            <p>This is a test email to verify your SMTP configuration is working correctly.</p>
            <p>If you received this email, your SMTP settings are properly configured!</p>
            <hr style="border: 1px solid #e5e7eb; margin: 20px 0;">
            <p style="color: #6b7280; font-size: 14px;">
              Sent from HashCoreX Email System<br>
              ${new Date().toLocaleString()}
            </p>
          </div>
        `,
        text: 'This is a test email from HashCoreX. If you received this, your SMTP configuration is working correctly!'
      });

      return success 
        ? { success: true, message: 'Test email sent successfully' }
        : { success: false, message: 'Failed to send test email' };
    } catch (error) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to send test email' 
      };
    }
  }

  private async getTemplateId(templateName: string): Promise<string | null> {
    try {
      const template = await prisma.emailTemplate.findFirst({
        where: { name: templateName },
        select: { id: true }
      });
      return template?.id || null;
    } catch {
      return null;
    }
  }

  private async logEmail(data: {
    userId?: string;
    recipient: string;
    subject: string;
    templateId?: string | null;
    status: 'PENDING' | 'SENT' | 'FAILED' | 'BOUNCED';
    errorMessage?: string;
    sentAt?: Date;
  }) {
    try {
      await prisma.emailLog.create({
        data: {
          userId: data.userId,
          recipient: data.recipient,
          subject: data.subject,
          templateId: data.templateId,
          status: data.status,
          errorMessage: data.errorMessage,
          sentAt: data.sentAt,
        }
      });
    } catch (error) {
      console.error('Failed to log email:', error);
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();

// Helper functions for specific email types
export const emailHelpers = {
  async sendWelcomeEmail(user: {
    id: string;
    email: string;
    firstName: string;
    referralId: string;
    createdAt: Date;
  }) {
    return await emailService.sendEmail({
      to: user.email,
      template: 'welcome',
      variables: {
        firstName: user.firstName,
        email: user.email,
        referralId: user.referralId,
        registrationDate: user.createdAt.toLocaleDateString(),
        dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`
      }
    });
  },

  async sendEmailVerification(email: string, verificationCode: string) {
    return await emailService.sendEmail({
      to: email,
      template: 'email-verification',
      variables: {
        verificationCode
      }
    });
  },

  async sendPasswordReset(email: string, resetCode: string) {
    return await emailService.sendEmail({
      to: email,
      template: 'password-reset',
      variables: {
        resetCode
      }
    });
  },

  async sendKYCApproved(user: { email: string; firstName: string; }) {
    return await emailService.sendEmail({
      to: user.email,
      template: 'kyc-approved',
      variables: {
        firstName: user.firstName,
        dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`
      }
    });
  },

  async sendKYCRejected(user: { email: string; firstName: string; }, rejectionReason: string) {
    return await emailService.sendEmail({
      to: user.email,
      template: 'kyc-rejected',
      variables: {
        firstName: user.firstName,
        rejectionReason,
        kycUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/profile`
      }
    });
  },

  async sendWithdrawalApproved(user: { email: string; firstName: string; }, withdrawal: {
    amount: number;
    fee: number;
    netAmount: number;
    walletAddress: string;
    transactionId: string;
    processedDate: Date;
  }) {
    return await emailService.sendEmail({
      to: user.email,
      template: 'withdrawal-approved',
      variables: {
        firstName: user.firstName,
        amount: withdrawal.amount.toFixed(2),
        fee: withdrawal.fee.toFixed(2),
        netAmount: withdrawal.netAmount.toFixed(2),
        walletAddress: withdrawal.walletAddress,
        transactionId: withdrawal.transactionId,
        processedDate: withdrawal.processedDate.toLocaleDateString()
      }
    });
  },

  async sendWithdrawalRejected(user: { email: string; firstName: string; }, withdrawal: {
    amount: number;
    requestedDate: Date;
  }, rejectionReason: string) {
    return await emailService.sendEmail({
      to: user.email,
      template: 'withdrawal-rejected',
      variables: {
        firstName: user.firstName,
        amount: withdrawal.amount.toFixed(2),
        requestedDate: withdrawal.requestedDate.toLocaleDateString(),
        rejectionReason,
        dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`
      }
    });
  },

  async sendMiningPurchaseConfirmation(user: { email: string; firstName: string; }, miningUnit: {
    thsAmount: number;
    investmentAmount: number;
    dailyROI: number;
    startDate: Date;
    expiryDate: Date;
  }) {
    const dailyEarnings = (miningUnit.investmentAmount * miningUnit.dailyROI / 100);

    return await emailService.sendEmail({
      to: user.email,
      template: 'mining-purchase',
      variables: {
        firstName: user.firstName,
        thsAmount: miningUnit.thsAmount.toFixed(1),
        investmentAmount: miningUnit.investmentAmount.toFixed(2),
        dailyROI: miningUnit.dailyROI.toFixed(2),
        dailyEarnings: dailyEarnings.toFixed(2),
        startDate: miningUnit.startDate.toLocaleDateString(),
        expiryDate: miningUnit.expiryDate.toLocaleDateString(),
        dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`
      }
    });
  },

  async sendWeeklyEarningsPayout(user: { email: string; firstName: string; }, earnings: {
    miningEarnings: number;
    referralBonuses: number;
    binaryBonuses: number;
    totalPayout: number;
    walletBalance: number;
    payoutDate: Date;
  }) {
    return await emailService.sendEmail({
      to: user.email,
      template: 'weekly-earnings',
      variables: {
        firstName: user.firstName,
        miningEarnings: earnings.miningEarnings.toFixed(2),
        referralBonuses: earnings.referralBonuses.toFixed(2),
        binaryBonuses: earnings.binaryBonuses.toFixed(2),
        totalPayout: earnings.totalPayout.toFixed(2),
        walletBalance: earnings.walletBalance.toFixed(2),
        payoutDate: earnings.payoutDate.toLocaleDateString(),
        dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`
      }
    });
  }
};
