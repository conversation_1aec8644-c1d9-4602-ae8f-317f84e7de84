(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/hooks/useNavigationAuth.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useNavigationAuth": (()=>useNavigationAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const useNavigationAuth = ({ user, loading, refreshUser })=>{
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const lastPathRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])('');
    const navigationTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useNavigationAuth.useEffect": ()=>{
            // Clear any existing timeout
            if (navigationTimeoutRef.current) {
                clearTimeout(navigationTimeoutRef.current);
            }
            // Don't redirect if still loading
            if (loading) return;
            // Check if this is a protected route
            const isProtectedRoute = pathname.startsWith('/dashboard') || pathname.startsWith('/admin');
            const isAuthRoute = pathname.startsWith('/login') || pathname.startsWith('/register');
            // If user is not authenticated and on a protected route
            if (!user && isProtectedRoute) {
                // Add a small delay to allow for authentication state recovery
                navigationTimeoutRef.current = setTimeout({
                    "useNavigationAuth.useEffect": ()=>{
                        // Double-check authentication state before redirecting
                        if (!user && !loading) {
                            console.log('Redirecting to login from protected route:', pathname);
                            router.push('/login');
                        }
                    }
                }["useNavigationAuth.useEffect"], 100);
            }
            // If user is authenticated and on auth route, redirect to dashboard
            if (user && isAuthRoute) {
                router.push('/dashboard');
            }
            // Update last path reference
            lastPathRef.current = pathname;
            // Cleanup timeout on unmount
            return ({
                "useNavigationAuth.useEffect": ()=>{
                    if (navigationTimeoutRef.current) {
                        clearTimeout(navigationTimeoutRef.current);
                    }
                }
            })["useNavigationAuth.useEffect"];
        }
    }["useNavigationAuth.useEffect"], [
        user,
        loading,
        pathname,
        router
    ]);
    // Handle browser navigation events
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useNavigationAuth.useEffect": ()=>{
            const handlePopState = {
                "useNavigationAuth.useEffect.handlePopState": async ()=>{
                    // When user navigates back/forward, refresh authentication state
                    console.log('Browser navigation detected, refreshing auth state');
                    await refreshUser();
                }
            }["useNavigationAuth.useEffect.handlePopState"];
            // Listen for browser back/forward navigation
            window.addEventListener('popstate', handlePopState);
            return ({
                "useNavigationAuth.useEffect": ()=>{
                    window.removeEventListener('popstate', handlePopState);
                }
            })["useNavigationAuth.useEffect"];
        }
    }["useNavigationAuth.useEffect"], [
        refreshUser
    ]);
    // Handle page visibility changes (tab switching, window focus)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useNavigationAuth.useEffect": ()=>{
            const handleVisibilityChange = {
                "useNavigationAuth.useEffect.handleVisibilityChange": async ()=>{
                    if (document.visibilityState === 'visible' && !loading) {
                        // Refresh auth state when page becomes visible
                        await refreshUser();
                    }
                }
            }["useNavigationAuth.useEffect.handleVisibilityChange"];
            document.addEventListener('visibilitychange', handleVisibilityChange);
            return ({
                "useNavigationAuth.useEffect": ()=>{
                    document.removeEventListener('visibilitychange', handleVisibilityChange);
                }
            })["useNavigationAuth.useEffect"];
        }
    }["useNavigationAuth.useEffect"], [
        refreshUser,
        loading
    ]);
};
_s(useNavigationAuth, "zfpcyEAak2IYnVumjPlCjmzTPRg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useClientOnly.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useClientOnly": (()=>useClientOnly),
    "useClientTime": (()=>useClientTime)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
;
function useClientOnly() {
    _s();
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useClientOnly.useEffect": ()=>{
            setIsClient(true);
        }
    }["useClientOnly.useEffect"], []);
    return isClient;
}
_s(useClientOnly, "k460N28PNzD7zo1YW47Q9UigQis=");
function useClientTime(calculateTime, interval = 1000) {
    _s1();
    const [timeValue, setTimeValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const isClient = useClientOnly();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useClientTime.useEffect": ()=>{
            if (!isClient) return;
            // Initial calculation
            setTimeValue(calculateTime());
            // Set up interval for updates
            const intervalId = setInterval({
                "useClientTime.useEffect.intervalId": ()=>{
                    setTimeValue(calculateTime());
                }
            }["useClientTime.useEffect.intervalId"], interval);
            return ({
                "useClientTime.useEffect": ()=>clearInterval(intervalId)
            })["useClientTime.useEffect"];
        }
    }["useClientTime.useEffect"], [
        isClient,
        calculateTime,
        interval
    ]);
    return timeValue;
}
_s1(useClientTime, "fGksU/9f+lSjdA130+iLkd/JoSo=", false, function() {
    return [
        useClientOnly
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculateROI": (()=>calculateROI),
    "calculateTHSPrice": (()=>calculateTHSPrice),
    "cn": (()=>cn),
    "copyToClipboard": (()=>copyToClipboard),
    "debounce": (()=>debounce),
    "formatCurrency": (()=>formatCurrency),
    "formatDate": (()=>formatDate),
    "formatDateTime": (()=>formatDateTime),
    "formatNumber": (()=>formatNumber),
    "formatTHS": (()=>formatTHS),
    "generateId": (()=>generateId),
    "getTimeUntilBinaryPayout": (()=>getTimeUntilBinaryPayout),
    "getTimeUntilNextPayout": (()=>getTimeUntilNextPayout),
    "sleep": (()=>sleep),
    "throttle": (()=>throttle),
    "truncateText": (()=>truncateText),
    "validateEmail": (()=>validateEmail),
    "validatePassword": (()=>validatePassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}
function formatNumber(num, decimals = 2) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(num);
}
function formatDate(date) {
    const d = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }).format(d);
}
function formatDateTime(date) {
    const d = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(d);
}
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + '...';
}
function generateId() {
    // Use crypto.randomUUID if available (modern browsers), fallback to timestamp + random
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
        return crypto.randomUUID().replace(/-/g, '').substring(0, 9);
    }
    // Fallback for older browsers or server-side
    const timestamp = Date.now().toString(36);
    const randomPart = Math.random().toString(36).substr(2, 5);
    return (timestamp + randomPart).substr(0, 9);
}
function sleep(ms) {
    return new Promise((resolve)=>setTimeout(resolve, ms));
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return (...args)=>{
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
function copyToClipboard(text) {
    if (navigator.clipboard) {
        return navigator.clipboard.writeText(text);
    }
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        document.execCommand('copy');
        return Promise.resolve();
    } catch (err) {
        return Promise.reject(err);
    } finally{
        document.body.removeChild(textArea);
    }
}
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function validatePassword(password) {
    const errors = [];
    if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
    }
    if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
    }
    if (!/\d/.test(password)) {
        errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        errors.push('Password must contain at least one special character');
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
function calculateROI(investment, dailyRate, days) {
    return investment * (dailyRate / 100) * days;
}
function calculateTHSPrice(ths, pricePerTHS) {
    return ths * pricePerTHS;
}
function formatTHS(ths) {
    if (ths >= 1000) {
        return `${(ths / 1000).toFixed(1)}K TH/s`;
    }
    return `${ths.toFixed(2)} TH/s`;
}
function getTimeUntilNextPayout() {
    const now = new Date();
    const nextSaturday = new Date();
    // Set to next Saturday at 15:00 UTC
    nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));
    nextSaturday.setUTCHours(15, 0, 0, 0);
    // If it's already past Saturday 15:00, move to next week
    if (now > nextSaturday) {
        nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);
    }
    const diff = nextSaturday.getTime() - now.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));
    const minutes = Math.floor(diff % (1000 * 60 * 60) / (1000 * 60));
    const seconds = Math.floor(diff % (1000 * 60) / 1000);
    return {
        days,
        hours,
        minutes,
        seconds
    };
}
function getTimeUntilBinaryPayout() {
    const now = new Date();
    const nextSaturday = new Date();
    // Set to next Saturday at 15:00 UTC
    nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));
    nextSaturday.setUTCHours(15, 0, 0, 0);
    // If it's already past Saturday 15:00, move to next week
    if (now > nextSaturday) {
        nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);
    }
    const diff = nextSaturday.getTime() - now.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));
    const minutes = Math.floor(diff % (1000 * 60 * 60) / (1000 * 60));
    const seconds = Math.floor(diff % (1000 * 60) / 1000);
    return {
        days,
        hours,
        minutes,
        seconds
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/types/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// User Types
__turbopack_context__.s({
    "DepositStatus": (()=>DepositStatus),
    "DocumentSide": (()=>DocumentSide),
    "DocumentType": (()=>DocumentType),
    "IDType": (()=>IDType),
    "KYCStatus": (()=>KYCStatus),
    "MiningUnitStatus": (()=>MiningUnitStatus),
    "TransactionStatus": (()=>TransactionStatus),
    "TransactionType": (()=>TransactionType),
    "UserRole": (()=>UserRole),
    "WithdrawalStatus": (()=>WithdrawalStatus)
});
var UserRole = /*#__PURE__*/ function(UserRole) {
    UserRole["USER"] = "USER";
    UserRole["ADMIN"] = "ADMIN";
    return UserRole;
}({});
var KYCStatus = /*#__PURE__*/ function(KYCStatus) {
    KYCStatus["PENDING"] = "PENDING";
    KYCStatus["APPROVED"] = "APPROVED";
    KYCStatus["REJECTED"] = "REJECTED";
    return KYCStatus;
}({});
var DocumentType = /*#__PURE__*/ function(DocumentType) {
    DocumentType["ID_DOCUMENT"] = "ID_DOCUMENT";
    DocumentType["SELFIE"] = "SELFIE";
    return DocumentType;
}({});
var IDType = /*#__PURE__*/ function(IDType) {
    IDType["NATIONAL_ID"] = "NATIONAL_ID";
    IDType["PASSPORT"] = "PASSPORT";
    IDType["DRIVING_LICENSE"] = "DRIVING_LICENSE";
    return IDType;
}({});
var DocumentSide = /*#__PURE__*/ function(DocumentSide) {
    DocumentSide["FRONT"] = "FRONT";
    DocumentSide["BACK"] = "BACK";
    return DocumentSide;
}({});
var MiningUnitStatus = /*#__PURE__*/ function(MiningUnitStatus) {
    MiningUnitStatus["ACTIVE"] = "ACTIVE";
    MiningUnitStatus["EXPIRED"] = "EXPIRED";
    return MiningUnitStatus;
}({});
var TransactionType = /*#__PURE__*/ function(TransactionType) {
    TransactionType["MINING_EARNINGS"] = "MINING_EARNINGS";
    TransactionType["DIRECT_REFERRAL"] = "DIRECT_REFERRAL";
    TransactionType["BINARY_BONUS"] = "BINARY_BONUS";
    TransactionType["DEPOSIT"] = "DEPOSIT";
    TransactionType["WITHDRAWAL"] = "WITHDRAWAL";
    TransactionType["PURCHASE"] = "PURCHASE";
    return TransactionType;
}({});
var TransactionStatus = /*#__PURE__*/ function(TransactionStatus) {
    TransactionStatus["PENDING"] = "PENDING";
    TransactionStatus["COMPLETED"] = "COMPLETED";
    TransactionStatus["FAILED"] = "FAILED";
    TransactionStatus["CANCELLED"] = "CANCELLED";
    return TransactionStatus;
}({});
var WithdrawalStatus = /*#__PURE__*/ function(WithdrawalStatus) {
    WithdrawalStatus["PENDING"] = "PENDING";
    WithdrawalStatus["APPROVED"] = "APPROVED";
    WithdrawalStatus["REJECTED"] = "REJECTED";
    WithdrawalStatus["COMPLETED"] = "COMPLETED";
    return WithdrawalStatus;
}({});
var DepositStatus = /*#__PURE__*/ function(DepositStatus) {
    DepositStatus["PENDING_VERIFICATION"] = "PENDING_VERIFICATION";
    DepositStatus["PENDING"] = "PENDING";
    DepositStatus["VERIFYING"] = "VERIFYING";
    DepositStatus["WAITING_FOR_CONFIRMATIONS"] = "WAITING_FOR_CONFIRMATIONS";
    DepositStatus["CONFIRMED"] = "CONFIRMED";
    DepositStatus["COMPLETED"] = "COMPLETED";
    DepositStatus["FAILED"] = "FAILED";
    DepositStatus["REJECTED"] = "REJECTED";
    return DepositStatus;
}({});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/(dashboard)/dashboard/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DashboardPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useAuth.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useNavigationAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useNavigationAuth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$DashboardLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/DashboardLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$DashboardOverview$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/DashboardOverview.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$PurchaseMiningUnit$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/PurchaseMiningUnit.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$EarningsTracker$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/EarningsTracker.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$WalletDashboard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/WalletDashboard.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$D3BinaryTree$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/D3BinaryTree.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$KYCPortal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/KYCPortal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$MiningUnitsTable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/MiningUnitsTable.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$SupportCenter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/SupportCenter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$UserProfileSettings$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/UserProfileSettings.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/ui/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Loading.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function DashboardPage() {
    _s();
    const { user, loading, refreshUser } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('overview');
    // Use the navigation auth hook to handle browser navigation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useNavigationAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNavigationAuth"])({
        user,
        loading,
        refreshUser
    });
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Loading"], {
                size: "lg",
                text: "Loading dashboard..."
            }, void 0, false, {
                fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                lineNumber: 28,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
            lineNumber: 27,
            columnNumber: 7
        }, this);
    }
    if (!user) {
        return null;
    }
    const renderTabContent = ()=>{
        switch(activeTab){
            case 'overview':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$DashboardOverview$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DashboardOverview"], {
                    onTabChange: setActiveTab
                }, void 0, false, {
                    fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                    lineNumber: 40,
                    columnNumber: 16
                }, this);
            case 'mining':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$PurchaseMiningUnit$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PurchaseMiningUnit"], {
                            onPurchaseComplete: ()=>window.location.reload()
                        }, void 0, false, {
                            fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                            lineNumber: 44,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$MiningUnitsTable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MiningUnitsTable"], {}, void 0, false, {
                            fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                            lineNumber: 45,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                    lineNumber: 43,
                    columnNumber: 11
                }, this);
            case 'earnings':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$EarningsTracker$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EarningsTracker"], {}, void 0, false, {
                    fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                    lineNumber: 49,
                    columnNumber: 16
                }, this);
            case 'wallet':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$WalletDashboard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WalletDashboard"], {}, void 0, false, {
                    fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                    lineNumber: 51,
                    columnNumber: 16
                }, this);
            case 'referrals':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$D3BinaryTree$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["D3BinaryTree"], {}, void 0, false, {
                    fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                    lineNumber: 53,
                    columnNumber: 16
                }, this);
            case 'kyc':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$KYCPortal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KYCPortal"], {}, void 0, false, {
                    fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                    lineNumber: 55,
                    columnNumber: 16
                }, this);
            case 'support':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$SupportCenter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SupportCenter"], {}, void 0, false, {
                    fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                    lineNumber: 57,
                    columnNumber: 16
                }, this);
            case 'profile':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$UserProfileSettings$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserProfileSettings"], {}, void 0, false, {
                    fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                    lineNumber: 59,
                    columnNumber: 16
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$DashboardOverview$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DashboardOverview"], {}, void 0, false, {
                    fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
                    lineNumber: 61,
                    columnNumber: 16
                }, this);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$DashboardLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DashboardLayout"], {
        activeTab: activeTab,
        onTabChange: setActiveTab,
        children: renderTabContent()
    }, void 0, false, {
        fileName: "[project]/src/app/(dashboard)/dashboard/page.tsx",
        lineNumber: 66,
        columnNumber: 5
    }, this);
}
_s(DashboardPage, "qnTFUAcnOrypnwm55En/BfpzcRE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useNavigationAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNavigationAuth"]
    ];
});
_c = DashboardPage;
var _c;
__turbopack_context__.k.register(_c, "DashboardPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_3e0a1ce3._.js.map