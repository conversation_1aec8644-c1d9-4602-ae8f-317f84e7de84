import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyAdminAuth } from '@/lib/auth';

// GET - Get all email templates
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ success: false, message: authResult.message }, { status: 401 });
    }

    const templates = await prisma.emailTemplate.findMany({
      orderBy: { name: 'asc' },
      select: {
        id: true,
        name: true,
        subject: true,
        htmlContent: true,
        textContent: true,
        variables: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    return NextResponse.json({
      success: true,
      data: templates
    });
  } catch (error) {
    console.error('Error fetching email templates:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch email templates' },
      { status: 500 }
    );
  }
}

// POST - Create new email template
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ success: false, message: authResult.message }, { status: 401 });
    }

    const body = await request.json();
    const { name, subject, htmlContent, textContent, variables } = body;

    // Validate required fields
    if (!name || !subject || !htmlContent) {
      return NextResponse.json(
        { success: false, message: 'Name, subject, and HTML content are required' },
        { status: 400 }
      );
    }

    // Check if template name already exists
    const existingTemplate = await prisma.emailTemplate.findFirst({
      where: { name }
    });

    if (existingTemplate) {
      return NextResponse.json(
        { success: false, message: 'Template with this name already exists' },
        { status: 400 }
      );
    }

    const newTemplate = await prisma.emailTemplate.create({
      data: {
        name,
        subject,
        htmlContent,
        textContent,
        variables: variables || [],
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        subject: true,
        htmlContent: true,
        textContent: true,
        variables: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Email template created successfully',
      data: newTemplate
    });
  } catch (error) {
    console.error('Error creating email template:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create email template' },
      { status: 500 }
    );
  }
}

// PUT - Update email template
export async function PUT(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ success: false, message: authResult.message }, { status: 401 });
    }

    const body = await request.json();
    const { id, name, subject, htmlContent, textContent, variables, isActive } = body;

    // Validate required fields
    if (!id || !name || !subject || !htmlContent) {
      return NextResponse.json(
        { success: false, message: 'ID, name, subject, and HTML content are required' },
        { status: 400 }
      );
    }

    // Check if template exists
    const existingTemplate = await prisma.emailTemplate.findUnique({
      where: { id }
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { success: false, message: 'Template not found' },
        { status: 404 }
      );
    }

    // Check if name is taken by another template
    if (name !== existingTemplate.name) {
      const nameConflict = await prisma.emailTemplate.findFirst({
        where: { 
          name,
          id: { not: id }
        }
      });

      if (nameConflict) {
        return NextResponse.json(
          { success: false, message: 'Template with this name already exists' },
          { status: 400 }
        );
      }
    }

    const updatedTemplate = await prisma.emailTemplate.update({
      where: { id },
      data: {
        name,
        subject,
        htmlContent,
        textContent,
        variables: variables || [],
        isActive: isActive !== undefined ? isActive : true,
      },
      select: {
        id: true,
        name: true,
        subject: true,
        htmlContent: true,
        textContent: true,
        variables: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Email template updated successfully',
      data: updatedTemplate
    });
  } catch (error) {
    console.error('Error updating email template:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update email template' },
      { status: 500 }
    );
  }
}

// DELETE - Delete email template
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ success: false, message: authResult.message }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Check if template exists
    const existingTemplate = await prisma.emailTemplate.findUnique({
      where: { id }
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { success: false, message: 'Template not found' },
        { status: 404 }
      );
    }

    await prisma.emailTemplate.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Email template deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting email template:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete email template' },
      { status: 500 }
    );
  }
}
